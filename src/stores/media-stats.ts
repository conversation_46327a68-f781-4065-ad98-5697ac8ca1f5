import { defineStore } from 'pinia'
import { ref } from 'vue'

export interface ProcessingStats {
  totalProcessed: number
  totalSize: number
  totalTime: number
  successRate: number
  averageProcessingTime: number
  tasksByType: Record<string, number>
  tasksByStatus: Record<string, number>
  lastUpdated?: number
}

/**
 * 媒体统计管理 Store
 * 负责处理统计数据的管理和持久化
 */
export const useMediaStatsStore = defineStore('media-stats', () => {
  // 统计状态
  const stats = ref<ProcessingStats>({
    totalProcessed: 0,
    totalSize: 0,
    totalTime: 0,
    successRate: 0,
    averageProcessingTime: 0,
    tasksByType: {},
    tasksByStatus: {}
  })

  const isLoaded = ref(false)

  // 加载统计数据
  const loadStats = async (): Promise<void> => {
    if (isLoaded.value) {
      console.log('[StatsStore] 统计数据已加载，跳过')
      return
    }

    try {
      console.log('[StatsStore] 开始从持久化存储加载统计数据...')
      const response = await window.electronAPI.media.getStats()

      if (response.success && response.data) {
        const loadedStats = response.data
        console.log('[StatsStore] 获取到持久化统计数据:', JSON.stringify(loadedStats))

        // 检查数据有效性
        if (typeof loadedStats === 'object' && loadedStats !== null) {
          Object.assign(stats.value, loadedStats)
          console.log(`[StatsStore] 统计数据加载成功: ${stats.value.totalProcessed} 已处理`)
        } else {
          console.warn('[StatsStore] 持久化统计数据无效，使用默认值')
        }
      } else if (!response.success) {
        console.warn('[StatsStore] 加载统计数据失败:', response.error)
      } else {
        console.log('[StatsStore] 没有持久化的统计数据，使用默认值')
      }
    } catch (error) {
      console.warn('[StatsStore] 加载统计数据出错:', error)
    } finally {
      isLoaded.value = true
      console.log('[StatsStore] 当前统计数据状态:', JSON.stringify(stats.value))
    }
  }

  // 刷新统计信息（基于当前任务数据计算）
  const refreshStats = async (allTasks: any[], allResults: any[]): Promise<void> => {
    try {
      // 检查输入参数是否有效
      if (!Array.isArray(allTasks)) {
        console.warn('[StatsStore] 刷新统计失败: allTasks 不是有效数组')
        return
      }

      if (!Array.isArray(allResults)) {
        console.warn('[StatsStore] 刷新统计失败: allResults 不是有效数组')
        return
      }

      // 只有当任务和结果都为空时才保留现有统计数据
      // 注意：如果有任务但没有结果，或者有结果但没有任务，仍应更新统计
      if (allTasks.length === 0 && allResults.length === 0) {
        console.log('[StatsStore] 任务和结果均为空，保留现有统计数据')
        return
      }

      console.log(`[StatsStore] 开始刷新统计: ${allTasks.length} 个任务, ${allResults.length} 个结果`)

      // 保存当前的历史统计数据
      const currentStats = { ...stats.value }
      console.log(`[StatsStore] 当前历史统计: 已处理 ${currentStats.totalProcessed} 个任务`)

      // 计算当前任务的统计
      const completedTasks = allTasks.filter(t => t.status === 'completed')
      const currentProcessed = completedTasks.length

      const currentSize = allResults
        .filter(r => r.success && r.size)
        .reduce((sum, r) => sum + (r.size || 0), 0)

      const completedWithDuration = allTasks.filter(t =>
        t.status === 'completed' && t.startTime && t.completedAt
      )

      const currentTime = completedWithDuration.reduce((sum, t) =>
        sum + ((t.completedAt || 0) - (t.startTime || 0)), 0
      )

      // 重新设计统计逻辑：
      // 统计数据应该直接反映当前所有任务的状态，而不是累加
      // 持久化的历史数据只在应用重启后没有任务时使用

      let totalProcessed: number
      let totalSize: number
      let totalTimeMs: number
      let tasksByType: Record<string, number>
      let tasksByStatus: Record<string, number>
      let successRate: number
      let averageProcessingTime: number

      if (allTasks.length === 0 && allResults.length === 0) {
        // 没有当前任务和结果，使用历史数据（应用刚启动的情况）
        console.log('[StatsStore] 使用历史统计数据')
        return // 前面已经有这个检查，这里不应该到达
      } else if (allTasks.length > 0) {
        // 有当前任务，直接基于当前所有任务计算统计
        totalProcessed = completedTasks.length
        totalSize = currentSize
        totalTimeMs = currentTime
        tasksByType = {}
        tasksByStatus = {}

        // 统计所有任务的类型和状态
        allTasks.forEach(task => {
          tasksByType[task.type] = (tasksByType[task.type] || 0) + 1
          tasksByStatus[task.status] = (tasksByStatus[task.status] || 0) + 1
        })

        // 成功率基于当前所有任务
        successRate = allTasks.length > 0 ? (completedTasks.length / allTasks.length) * 100 : 0

        // 平均处理时间基于当前完成的任务
        averageProcessingTime = completedWithDuration.length > 0 ?
          currentTime / completedWithDuration.length : 0

        // 如果当前统计小于历史统计，说明是重启后的部分数据，需要保持历史最大值
        if (currentStats.totalProcessed > 0) {
          totalProcessed = Math.max(currentStats.totalProcessed, totalProcessed)
          totalSize = Math.max(currentStats.totalSize, totalSize)
          totalTimeMs = Math.max(currentStats.totalTime * 1000, totalTimeMs)

          // 对于类型和状态统计，如果历史数据更大，则保留历史数据
          Object.keys(currentStats.tasksByType || {}).forEach(type => {
            const historicalCount = currentStats.tasksByType[type] || 0
            const currentCount = tasksByType[type] || 0
            tasksByType[type] = Math.max(historicalCount, currentCount)
          })

          Object.keys(currentStats.tasksByStatus || {}).forEach(status => {
            const historicalCount = currentStats.tasksByStatus[status] || 0
            const currentCount = tasksByStatus[status] || 0
            tasksByStatus[status] = Math.max(historicalCount, currentCount)
          })

          // 重新计算成功率和平均时间
          const totalCompleted = tasksByStatus['completed'] || 0
          const totalTasks = Object.values(tasksByStatus).reduce((sum, count) => sum + count, 0)
          successRate = totalTasks > 0 ? (totalCompleted / totalTasks) * 100 : currentStats.successRate
          averageProcessingTime = totalCompleted > 0 ?
            totalTimeMs / totalCompleted : currentStats.averageProcessingTime * 1000
        }
      } else {
        // 有结果但没有任务（不太可能的情况），使用历史数据
        totalProcessed = currentStats.totalProcessed
        totalSize = currentStats.totalSize
        totalTimeMs = currentStats.totalTime * 1000
        tasksByType = currentStats.tasksByType || {}
        tasksByStatus = currentStats.tasksByStatus || {}
        successRate = currentStats.successRate
        averageProcessingTime = currentStats.averageProcessingTime * 1000
      }

      console.log(`[StatsStore] 统计计算结果: 总处理 ${totalProcessed} (历史: ${currentStats.totalProcessed}, 当前: ${currentProcessed})`)

      // 更新统计信息
      stats.value = {
        totalProcessed,
        totalSize,
        totalTime: Math.floor(totalTimeMs / 1000), // 转换为秒
        successRate: Math.round(successRate * 100) / 100,
        averageProcessingTime: Math.floor(averageProcessingTime / 1000), // 转换为秒
        tasksByType,
        tasksByStatus,
        lastUpdated: Date.now()
      }

      // 持久化统计数据
      await persistStats()

      console.log('[StatsStore] 统计信息已刷新:', JSON.stringify(stats.value))
    } catch (error) {
      console.error('[StatsStore] 刷新统计失败:', error)
    }
  }

  // 持久化统计数据
  const persistStats = async (): Promise<void> => {
    try {
      if (typeof window.electronAPI.media.persistStats === 'function') {
        console.log('[StatsStore] 开始持久化统计数据...')

        // 创建一个普通对象，避免 Vue 的响应式包装器
        const plainStats = JSON.parse(JSON.stringify(stats.value))

        console.log('[StatsStore] 待持久化的统计数据:', JSON.stringify(plainStats))

        const response = await window.electronAPI.media.persistStats(plainStats)

        if (response && response.success) {
          console.log('[StatsStore] 统计数据持久化成功')
        } else if (response) {
          console.warn('[StatsStore] 统计数据持久化失败:', response.error)
        }
      } else {
        console.warn('[StatsStore] persistStats API 不可用')
      }
    } catch (error) {
      console.error('[StatsStore] 持久化统计数据失败:', error)
    }
  }

  // 更新统计数据
  const updateStats = async (newStats: Partial<ProcessingStats>): Promise<void> => {
    Object.assign(stats.value, newStats, { lastUpdated: Date.now() })
    await persistStats()
  }

  // 重置统计数据
  const resetStats = async (): Promise<void> => {
    stats.value = {
      totalProcessed: 0,
      totalSize: 0,
      totalTime: 0,
      successRate: 0,
      averageProcessingTime: 0,
      tasksByType: {},
      tasksByStatus: {},
      lastUpdated: Date.now()
    }
    await persistStats()
    console.log('[StatsStore] 统计数据已重置')
  }

  // 清空统计数据（通过 IPC 持久化）
  const clearStats = async (): Promise<boolean> => {
    try {
      const response = await window.electronAPI.media.clearStats()

      if (response.success) {
        // 同步重置本地状态
        await resetStats()
        console.log('[StatsStore] 统计数据已通过 IPC 清空')
        return true
      } else {
        console.error('[StatsStore] 清空统计数据失败:', response.error)
        return false
      }
    } catch (error) {
      console.error('[StatsStore] 清空统计数据出错:', error)
      return false
    }
  }

  // 格式化统计数据
  const getFormattedStats = () => {
    return {
      totalProcessed: stats.value.totalProcessed,
      totalSize: formatFileSize(stats.value.totalSize),
      totalTime: formatDuration(stats.value.totalTime),
      successRate: `${stats.value.successRate.toFixed(1)}%`,
      averageProcessingTime: formatDuration(stats.value.averageProcessingTime),
      tasksByType: stats.value.tasksByType,
      tasksByStatus: stats.value.tasksByStatus,
      lastUpdated: stats.value.lastUpdated ? new Date(stats.value.lastUpdated).toLocaleString() : '未知'
    }
  }

  // 格式化文件大小
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  // 格式化时长
  const formatDuration = (seconds: number): string => {
    if (seconds < 60) return `${seconds}秒`
    if (seconds < 3600) return `${Math.floor(seconds / 60)}分钟`
    return `${Math.floor(seconds / 3600)}小时`
  }

  return {
    // 状态
    stats,
    isLoaded,

    // 方法
    loadStats,
    refreshStats,
    updateStats,
    resetStats,
    persistStats,
    getFormattedStats,
    formatFileSize,
    formatDuration,
    clearStats
  }
})