import { defineStore } from 'pinia'
import { ref } from 'vue'

export interface ProcessingStats {
  totalProcessed: number
  totalSize: number
  totalTime: number
  successRate: number
  averageProcessingTime: number
  tasksByType: Record<string, number>
  tasksByStatus: Record<string, number>
  lastUpdated?: number
}

/**
 * 媒体统计管理 Store
 * 负责处理统计数据的管理和持久化
 */
export const useMediaStatsStore = defineStore('media-stats', () => {
  // 统计状态
  const stats = ref<ProcessingStats>({
    totalProcessed: 0,
    totalSize: 0,
    totalTime: 0,
    successRate: 0,
    averageProcessingTime: 0,
    tasksByType: {},
    tasksByStatus: {}
  })

  const isLoaded = ref(false)

  // 加载统计数据
  const loadStats = async (): Promise<void> => {
    if (isLoaded.value) {
      console.log('[StatsStore] 统计数据已加载，跳过')
      return
    }

    try {
      console.log('[StatsStore] 开始从持久化存储加载统计数据...')
      const response = await window.electronAPI.media.getStats()

      if (response.success && response.data) {
        const loadedStats = response.data
        console.log('[StatsStore] 获取到持久化统计数据:', JSON.stringify(loadedStats))

        // 检查数据有效性
        if (typeof loadedStats === 'object' && loadedStats !== null) {
          Object.assign(stats.value, loadedStats)
          console.log(`[StatsStore] 统计数据加载成功: ${stats.value.totalProcessed} 已处理`)
        } else {
          console.warn('[StatsStore] 持久化统计数据无效，使用默认值')
        }
      } else if (!response.success) {
        console.warn('[StatsStore] 加载统计数据失败:', response.error)
      } else {
        console.log('[StatsStore] 没有持久化的统计数据，使用默认值')
      }
    } catch (error) {
      console.warn('[StatsStore] 加载统计数据出错:', error)
    } finally {
      isLoaded.value = true
      console.log('[StatsStore] 当前统计数据状态:', JSON.stringify(stats.value))
    }
  }

  // 刷新统计信息（基于当前任务数据计算）
  const refreshStats = async (allTasks: any[], allResults: any[]): Promise<void> => {
    try {
      // 检查输入参数是否有效
      if (!Array.isArray(allTasks)) {
        console.warn('[StatsStore] 刷新统计失败: allTasks 不是有效数组')
        return
      }

      if (!Array.isArray(allResults)) {
        console.warn('[StatsStore] 刷新统计失败: allResults 不是有效数组')
        return
      }

      // 只有当任务和结果都为空时才保留现有统计数据
      // 注意：如果有任务但没有结果，或者有结果但没有任务，仍应更新统计
      if (allTasks.length === 0 && allResults.length === 0) {
        console.log('[StatsStore] 任务和结果均为空，保留现有统计数据')
        return
      }

      console.log(`[StatsStore] 开始刷新统计: ${allTasks.length} 个任务, ${allResults.length} 个结果`)

      // 保存当前的历史统计数据
      const currentStats = { ...stats.value }
      console.log(`[StatsStore] 当前历史统计: 已处理 ${currentStats.totalProcessed} 个任务`)

      // 计算当前任务的统计
      const completedTasks = allTasks.filter(t => t.status === 'completed')
      const currentProcessed = completedTasks.length

      const currentSize = allResults
        .filter(r => r.success && r.size)
        .reduce((sum, r) => sum + (r.size || 0), 0)

      const completedWithDuration = allTasks.filter(t =>
        t.status === 'completed' && t.startTime && t.completedAt
      )

      const currentTime = completedWithDuration.reduce((sum, t) =>
        sum + ((t.completedAt || 0) - (t.startTime || 0)), 0
      )

      // 重新设计统计逻辑：
      // 1. 如果没有历史数据，直接使用当前数据
      // 2. 如果有历史数据，需要累加当前任务到历史统计中

      let totalProcessed: number
      let totalSize: number
      let totalTimeMs: number
      let tasksByType: Record<string, number>
      let tasksByStatus: Record<string, number>

      if (currentStats.totalProcessed === 0) {
        // 没有历史数据，直接使用当前数据
        totalProcessed = currentProcessed
        totalSize = currentSize
        totalTimeMs = currentTime
        tasksByType = {}
        tasksByStatus = {}

        allTasks.forEach(task => {
          tasksByType[task.type] = (tasksByType[task.type] || 0) + 1
          tasksByStatus[task.status] = (tasksByStatus[task.status] || 0) + 1
        })
      } else {
        // 有历史数据，始终采用累加策略
        // 这样可以处理重启后的各种情况：重启后无新任务、重启后有新任务等

        // 累加总数统计
        totalProcessed = currentStats.totalProcessed + currentProcessed
        totalSize = currentStats.totalSize + currentSize
        totalTimeMs = (currentStats.totalTime * 1000) + currentTime

        // 保留历史的类型和状态统计
        tasksByType = { ...currentStats.tasksByType }
        tasksByStatus = { ...currentStats.tasksByStatus }

        // 累加当前会话的新任务
        allTasks.forEach(task => {
          tasksByType[task.type] = (tasksByType[task.type] || 0) + 1
          tasksByStatus[task.status] = (tasksByStatus[task.status] || 0) + 1
        })
      }

      // 成功率计算：基于总的完成任务数和总任务数
      let successRate: number
      if (currentStats.totalProcessed === 0) {
        // 没有历史数据，基于当前任务计算
        successRate = allTasks.length > 0 ? (completedTasks.length / allTasks.length) * 100 : 0
      } else {
        // 有历史数据，基于完成状态的任务数计算
        const totalCompleted = tasksByStatus['completed'] || 0
        successRate = totalProcessed > 0 ? (totalCompleted / totalProcessed) * 100 : currentStats.successRate
      }

      // 平均处理时间：基于总时间和总完成任务数
      let averageProcessingTime: number
      if (currentStats.totalProcessed === 0) {
        // 没有历史数据，基于当前任务计算
        averageProcessingTime = completedWithDuration.length > 0 ?
          currentTime / completedWithDuration.length : 0
      } else {
        // 有历史数据，基于总时间和总任务数计算
        const totalCompleted = (tasksByStatus['completed'] || 0)
        averageProcessingTime = totalCompleted > 0 ?
          totalTimeMs / totalCompleted : currentStats.averageProcessingTime * 1000
      }

      console.log(`[StatsStore] 统计计算结果: 总处理 ${totalProcessed} (历史: ${currentStats.totalProcessed}, 当前: ${currentProcessed})`)

      // 更新统计信息
      stats.value = {
        totalProcessed,
        totalSize,
        totalTime: Math.floor(totalTimeMs / 1000), // 转换为秒
        successRate: Math.round(successRate * 100) / 100,
        averageProcessingTime: Math.floor(averageProcessingTime / 1000), // 转换为秒
        tasksByType,
        tasksByStatus,
        lastUpdated: Date.now()
      }

      // 持久化统计数据
      await persistStats()

      console.log('[StatsStore] 统计信息已刷新:', JSON.stringify(stats.value))
    } catch (error) {
      console.error('[StatsStore] 刷新统计失败:', error)
    }
  }

  // 持久化统计数据
  const persistStats = async (): Promise<void> => {
    try {
      if (typeof window.electronAPI.media.persistStats === 'function') {
        console.log('[StatsStore] 开始持久化统计数据...')

        // 创建一个普通对象，避免 Vue 的响应式包装器
        const plainStats = JSON.parse(JSON.stringify(stats.value))

        console.log('[StatsStore] 待持久化的统计数据:', JSON.stringify(plainStats))

        const response = await window.electronAPI.media.persistStats(plainStats)

        if (response && response.success) {
          console.log('[StatsStore] 统计数据持久化成功')
        } else if (response) {
          console.warn('[StatsStore] 统计数据持久化失败:', response.error)
        }
      } else {
        console.warn('[StatsStore] persistStats API 不可用')
      }
    } catch (error) {
      console.error('[StatsStore] 持久化统计数据失败:', error)
    }
  }

  // 更新统计数据
  const updateStats = async (newStats: Partial<ProcessingStats>): Promise<void> => {
    Object.assign(stats.value, newStats, { lastUpdated: Date.now() })
    await persistStats()
  }

  // 重置统计数据
  const resetStats = async (): Promise<void> => {
    stats.value = {
      totalProcessed: 0,
      totalSize: 0,
      totalTime: 0,
      successRate: 0,
      averageProcessingTime: 0,
      tasksByType: {},
      tasksByStatus: {},
      lastUpdated: Date.now()
    }
    await persistStats()
    console.log('[StatsStore] 统计数据已重置')
  }

  // 清空统计数据（通过 IPC 持久化）
  const clearStats = async (): Promise<boolean> => {
    try {
      const response = await window.electronAPI.media.clearStats()

      if (response.success) {
        // 同步重置本地状态
        await resetStats()
        console.log('[StatsStore] 统计数据已通过 IPC 清空')
        return true
      } else {
        console.error('[StatsStore] 清空统计数据失败:', response.error)
        return false
      }
    } catch (error) {
      console.error('[StatsStore] 清空统计数据出错:', error)
      return false
    }
  }

  // 格式化统计数据
  const getFormattedStats = () => {
    return {
      totalProcessed: stats.value.totalProcessed,
      totalSize: formatFileSize(stats.value.totalSize),
      totalTime: formatDuration(stats.value.totalTime),
      successRate: `${stats.value.successRate.toFixed(1)}%`,
      averageProcessingTime: formatDuration(stats.value.averageProcessingTime),
      tasksByType: stats.value.tasksByType,
      tasksByStatus: stats.value.tasksByStatus,
      lastUpdated: stats.value.lastUpdated ? new Date(stats.value.lastUpdated).toLocaleString() : '未知'
    }
  }

  // 格式化文件大小
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  // 格式化时长
  const formatDuration = (seconds: number): string => {
    if (seconds < 60) return `${seconds}秒`
    if (seconds < 3600) return `${Math.floor(seconds / 60)}分钟`
    return `${Math.floor(seconds / 3600)}小时`
  }

  return {
    // 状态
    stats,
    isLoaded,

    // 方法
    loadStats,
    refreshStats,
    updateStats,
    resetStats,
    persistStats,
    getFormattedStats,
    formatFileSize,
    formatDuration,
    clearStats
  }
})