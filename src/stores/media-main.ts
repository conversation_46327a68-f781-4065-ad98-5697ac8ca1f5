import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { useMediaTasksStore } from './media-tasks'
import { useMediaSettingsStore } from './media-settings'
import { useMediaStatsStore } from './media-stats'

/**
 * 主媒体 Store
 * 协调和统一管理所有媒体相关的子 stores
 */
export const useMediaMainStore = defineStore('media-main', () => {
  // 子 stores
  const tasksStore = useMediaTasksStore()
  const settingsStore = useMediaSettingsStore()
  const statsStore = useMediaStatsStore()

  // 初始化状态
  const isInitialized = ref(false)

  // 统一的计算属性（委托给子 stores）
  const allTasks = computed(() => tasksStore.allTasks)
  const allBatchTasks = computed(() => tasksStore.allBatchTasks)
  const allResults = computed(() => tasksStore.allResults)
  const activeTasks = computed(() => tasksStore.activeTasks)
  const settings = computed(() => settingsStore.settings)
  const stats = computed(() => statsStore.stats)

  // 初始化所有子 stores
  const initialize = async (): Promise<void> => {
    if (isInitialized.value) return

    try {
      console.log('[MediaMainStore] 开始初始化...')

      // 先加载设置
      await settingsStore.loadSettings()

      // 恢复未完成的任务
      await restoreUncompletedTasks()

      // 恢复历史处理结果
      await restoreTaskResults()

      // 获取所有任务和结果
      const allSingleTasks = Array.from(tasksStore.singleTasks.values())
      const allBatchSubTasks = Array.from(tasksStore.batchTasks.values()).flatMap(batch => batch.tasks)
      const tasks = [...allSingleTasks, ...allBatchSubTasks]

      const results = Array.from(tasksStore.taskResults.values())

      // 调试：输出任务和结果数量
      console.log(`[MediaMainStore] 初始化后数据状态: ${tasks.length} 个任务 (${allSingleTasks.length} 单任务 + ${allBatchSubTasks.length} 批量子任务), ${results.length} 个结果`)

      // 在任务和结果加载完成后，处理统计数据
      // 首先尝试从持久化存储加载历史统计数据
      console.log('[MediaMainStore] 从持久化存储加载历史统计数据')
      await statsStore.loadStats()

      // 如果有当前任务或结果数据，则基于这些数据刷新统计
      // 这样可以确保统计数据包含历史数据和当前数据
      if (tasks.length > 0 || results.length > 0) {
        console.log('[MediaMainStore] 基于当前任务和结果数据刷新统计（保留历史数据）')
        await statsStore.refreshStats(tasks, results)
      } else {
        console.log('[MediaMainStore] 没有当前任务数据，使用已加载的历史统计数据')
      }

      // 调试：输出加载后的统计数据
      console.log('[MediaMainStore] 统计数据加载完成:', JSON.stringify(statsStore.stats))

      // 启动自动清理（如果启用）
      if (settingsStore.settings.autoCleanupCompleted) {
        startAutoCleanup()
      }

      isInitialized.value = true
      console.log('[MediaMainStore] 初始化完成')
    } catch (error) {
      console.error('[MediaMainStore] 初始化失败:', error)
      throw error
    }
  }

  // 恢复未完成的任务
  const restoreUncompletedTasks = async (): Promise<void> => {
    try {
      console.log('[MediaMainStore] 开始恢复未完成任务...')
      const response = await window.electronAPI.media.getUncompletedTasks()

      if (response.success && response.data) {
        const { singleTasks: restoredSingle = [], batchTasks: restoredBatch = [] } = response.data

        // 恢复单文件任务
        if (Array.isArray(restoredSingle) && restoredSingle.length > 0) {
          restoredSingle.forEach((task: any) => {
            tasksStore.singleTasks.set(task.id, task)
          })
        }

        // 恢复批量任务
        if (Array.isArray(restoredBatch) && restoredBatch.length > 0) {
          restoredBatch.forEach((batch: any) => {
            tasksStore.batchTasks.set(batch.id, batch)
          })
        }

        console.log(`[MediaMainStore] 恢复任务成功: ${restoredSingle.length} 单文件, ${restoredBatch.length} 批量`)
      } else if (!response.success) {
        console.warn('[MediaMainStore] 恢复任务失败:', response.error)
      } else {
        console.log('[MediaMainStore] 没有未完成的任务需要恢复')
      }
    } catch (error) {
      console.error('[MediaMainStore] 恢复任务出现异常:', error)
    }
  }

  // 恢复历史处理结果
  const restoreTaskResults = async (): Promise<void> => {
    try {
      console.log('[MediaMainStore] 开始恢复历史处理结果...')
      const response = await window.electronAPI.media.getTaskResults()

      if (response.success && response.data) {
        const restoredResults = Array.isArray(response.data) ? response.data : []

        if (restoredResults.length > 0) {
          restoredResults.forEach((result: any) => {
            tasksStore.taskResults.set(result.id, result)
          })
          console.log(`[MediaMainStore] 恢复处理结果成功: ${restoredResults.length} 个`)
        } else {
          console.log('[MediaMainStore] 没有历史处理结果需要恢复')
        }
      } else if (!response.success) {
        console.warn('[MediaMainStore] 恢复处理结果失败:', response.error)
      } else {
        console.log('[MediaMainStore] 没有历史处理结果数据')
      }
    } catch (error) {
      console.error('[MediaMainStore] 恢复处理结果出现异常:', error)
    }
  }

  // 启动自动清理
  const startAutoCleanup = (): void => {
    setInterval(() => {
      cleanupCompletedTasks()
    }, 60 * 60 * 1000) // 每小时检查一次
  }

  // 清理已完成的任务
  const cleanupCompletedTasks = async (): Promise<void> => {
    if (!settingsStore.settings.autoCleanupCompleted) return

    const cutoffTime = Date.now() - (settingsStore.settings.cleanupAfterHours * 60 * 60 * 1000)

    // 清理单文件任务
    const singleTasksToCleanup: string[] = []
    tasksStore.singleTasks.forEach((task, id) => {
      if (['completed', 'error'].includes(task.status) &&
        task.completedAt && task.completedAt < cutoffTime) {
        singleTasksToCleanup.push(id)
      }
    })

    // 清理批量任务
    const batchTasksToCleanup: string[] = []
    tasksStore.batchTasks.forEach((batch, id) => {
      if (['completed', 'error'].includes(batch.status) &&
        batch.completedAt && batch.completedAt < cutoffTime) {
        batchTasksToCleanup.push(id)
      }
    })

    // 执行清理
    singleTasksToCleanup.forEach(taskId => {
      tasksStore.removeSingleTask(taskId)
    })

    batchTasksToCleanup.forEach(batchId => {
      tasksStore.removeBatchTask(batchId)
    })

    if (singleTasksToCleanup.length > 0 || batchTasksToCleanup.length > 0) {
      console.log(`[MediaMainStore] 自动清理完成: ${singleTasksToCleanup.length} 单文件任务, ${batchTasksToCleanup.length} 批量任务`)

      // 清理后获取剩余的任务和结果
      const allSingleTasks = Array.from(tasksStore.singleTasks.values())
      const allBatchSubTasks = Array.from(tasksStore.batchTasks.values()).flatMap(batch => batch.tasks)
      const tasks = [...allSingleTasks, ...allBatchSubTasks]

      const results = Array.from(tasksStore.taskResults.values())

      // 清理后检查是否还有任务数据，再决定是否刷新统计
      if (tasks.length > 0 || results.length > 0) {
        console.log(`[MediaMainStore] 清理后剩余 ${tasks.length} 个任务, ${results.length} 个结果，刷新统计`)
        await statsStore.refreshStats(tasks, results)
      } else {
        console.log('[MediaMainStore] 清理后没有任务数据，跳过统计刷新')
      }
    }
  }

  // 刷新统计信息
  const refreshStats = async (): Promise<void> => {
    try {
      // 直接从tasksStore中获取原始数据
      const allSingleTasks = Array.from(tasksStore.singleTasks.values())
      const allBatchSubTasks = Array.from(tasksStore.batchTasks.values()).flatMap(batch => batch.tasks)
      const tasks = [...allSingleTasks, ...allBatchSubTasks]

      const results = Array.from(tasksStore.taskResults.values())

      console.log(`[MediaMainStore] 准备刷新统计: 发现 ${tasks.length} 个任务 (${allSingleTasks.length} 单任务 + ${allBatchSubTasks.length} 批量子任务), ${results.length} 个结果`)

      // 只有当任务或结果不为空才更新统计
      if (tasks.length > 0 || results.length > 0) {
        await statsStore.refreshStats(
          tasks,
          results
        )
        console.log(`[MediaMainStore] 刷新统计完成: ${tasks.length} 个任务, ${results.length} 个结果`)
      } else {
        console.log('[MediaMainStore] 跳过统计更新: 没有任务或结果数据')
      }
    } catch (error) {
      console.error('[MediaMainStore] 刷新统计出错:', error)
    }
  }

  // 任务执行相关方法
  const executeTask = async (task: any): Promise<any> => {
    const plainOptions = JSON.parse(JSON.stringify(task.options))

    switch (task.type) {
      case 'video-convert':
        return await window.electronAPI.media.convertVideo(
          task.filePath,
          task.outputPath,
          plainOptions
        )
      case 'audio-extract':
        return await window.electronAPI.media.extractAudio(
          task.filePath,
          task.outputPath,
          plainOptions
        )
      case 'asr':
        return await window.electronAPI.media.extractText(
          task.filePath,
          plainOptions
        )
      case 'image-process':
        return await window.electronAPI.media.processImages(
          [task.filePath],
          plainOptions
        )
      default:
        throw new Error(`不支持的任务类型: ${task.type}`)
    }
  }

  // 启动单文件任务
  const startSingleTask = async (taskId: string): Promise<void> => {
    const task = tasksStore.singleTasks.get(taskId)
    if (!task) {
      throw new Error(`单文件任务不存在: ${taskId}`)
    }

    if (tasksStore.activeTasks.length >= settingsStore.settings.maxConcurrentTasks) {
      throw new Error('已达到最大并发任务数量限制')
    }

    task.status = 'processing'
    task.startTime = Date.now()
    tasksStore.activeTasksCount++

    tasksStore.updateProcessingQueue(task.type, taskId, 'add')

    // 状态变更后立即持久化
    await tasksStore.persistSingleTask(taskId)

    try {
      const response = await executeTask(task)

      if (response.success) {
        task.status = 'completed'
        task.progress = 100
        task.completedAt = Date.now()

        const result = tasksStore.createTaskResult(task, response)
        tasksStore.taskResults.set(result.id, result)
        task.result = result

        // 任务完成后持久化结果
        await tasksStore.persistTaskResult(result.id)
        await tasksStore.persistSingleTask(taskId)

        console.log(`[MediaMainStore] 单文件任务完成: ${taskId}`)
      } else {
        throw new Error(response.error)
      }
    } catch (error: any) {
      task.status = 'error'
      task.error = error.message
      task.completedAt = Date.now()

      const result = tasksStore.createTaskResult(task, { success: false, error: error.message })
      tasksStore.taskResults.set(result.id, result)
      task.result = result

      // 任务失败后持久化结果
      await tasksStore.persistTaskResult(result.id)
      await tasksStore.persistSingleTask(taskId)

      console.error(`[MediaMainStore] 单文件任务失败: ${taskId}`, error)
    } finally {
      tasksStore.activeTasksCount--
      tasksStore.updateProcessingQueue(task.type, taskId, 'remove')

      // 确保在任务完成后刷新统计
      console.log(`[MediaMainStore] 任务 ${taskId} 完成，准备刷新统计`)

      // 获取最新的任务和结果数据
      const allSingleTasks = Array.from(tasksStore.singleTasks.values())
      const allBatchSubTasks = Array.from(tasksStore.batchTasks.values()).flatMap(batch => batch.tasks)
      const tasks = [...allSingleTasks, ...allBatchSubTasks]

      const results = Array.from(tasksStore.taskResults.values())

      // 直接调用statsStore.refreshStats
      await statsStore.refreshStats(tasks, results)

      // 通知统计数据已更新
      try {
        await window.electronAPI.media.notifyStatsUpdate()
      } catch (error) {
        console.warn('[MediaMainStore] 通知统计更新失败:', error)
      }
    }
  }

  // 暂停单文件任务
  const pauseSingleTask = async (taskId: string): Promise<void> => {
    const task = tasksStore.singleTasks.get(taskId)
    if (!task) {
      throw new Error(`单文件任务不存在: ${taskId}`)
    }

    if (task.status === 'processing') {
      task.status = 'paused'
      tasksStore.activeTasksCount--
      tasksStore.updateProcessingQueue(task.type, taskId, 'remove')

      // 暂停后持久化状态
      await tasksStore.persistSingleTask(taskId)

      console.log(`[MediaMainStore] 单文件任务已暂停: ${taskId}`)
    }
  }

  // 重试单文件任务
  const retrySingleTask = async (taskId: string): Promise<void> => {
    const task = tasksStore.singleTasks.get(taskId)
    if (!task) {
      throw new Error(`单文件任务不存在: ${taskId}`)
    }

    if (!['error', 'paused', 'completed'].includes(task.status)) {
      throw new Error(`只能重试失败、已暂停或已完成的任务`)
    }

    // 重置任务状态
    task.status = 'pending'
    task.progress = 0
    task.error = undefined
    task.result = undefined

    // 重试前持久化状态
    await tasksStore.persistSingleTask(taskId)

    console.log(`[MediaMainStore] 准备重试单文件任务: ${taskId}`)

    // 立即启动任务
    await startSingleTask(taskId)
  }

  // 暂停所有任务
  const pauseAllTasks = async (): Promise<void> => {
    // 暂停单文件任务
    const processingTasks = Array.from(tasksStore.singleTasks.values())
      .filter(task => task.status === 'processing')

    for (const task of processingTasks) {
      await pauseSingleTask(task.id)
    }

    // 暂停批量任务
    const processingBatches = Array.from(tasksStore.batchTasks.values())
      .filter(batch => batch.status === 'processing')

    for (const batch of processingBatches) {
      batch.status = 'paused'

      // 更新批量任务中的所有处理中子任务状态
      batch.tasks.forEach(task => {
        if (task.status === 'processing') {
          task.status = 'paused'
        }
      })

      // 持久化批量任务状态
      await tasksStore.persistBatchTask(batch.id)
    }

    console.log(`[MediaMainStore] 已暂停所有任务: ${processingTasks.length} 单文件, ${processingBatches.length} 批量`)
  }

  return {
    // 子 stores
    tasksStore,
    settingsStore,
    statsStore,

    // 状态
    isInitialized,

    // 计算属性
    allTasks,
    allBatchTasks,
    allResults,
    activeTasks,
    settings,
    stats,

    // 方法
    initialize,
    refreshStats,
    startSingleTask,
    pauseSingleTask,
    retrySingleTask,
    pauseAllTasks,
    cleanupCompletedTasks
  }
})