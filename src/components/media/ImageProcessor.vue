<template>
  <div class="app-card image-processor">
    <div class="section-header">
      <h3>
        <Icon icon="mdi:image-outline" class="section-icon" />
        图片处理
      </h3>
      <el-button size="small" @click="clearAll" v-if="fileList.length > 0">
        <Icon icon="mdi:trash-can-outline" />
        清空
      </el-button>
    </div>

    <!-- 文件上传 -->
    <FileUploader
      v-model="fileList"
      task-type="image-process"
      :max-file-size="50"
      :max-file-count="50"
      upload-text="拖拽图片文件到此处或点击上传"
      hint="支持 JPG, PNG, WebP, BMP, GIF, TIFF, SVG 格式，单个文件最大 50MB"
      @file-added="handleFileAdded"
      @file-removed="handleFileRemoved"
      @error="handleError"
    />

    <!-- 处理选项 -->
    <div class="options-section" v-if="fileList.length > 0">
      <el-divider>处理设置</el-divider>
      <ProcessingOptions
        v-model="processingOptions"
        task-type="image-process"
        :show-presets="true"
        :show-save-preset="true"
        @preset-saved="handlePresetSaved"
      />
    </div>

    <!-- 输出目录 -->
    <OutputDirectorySelector
      v-model="outputDirectory"
      v-if="fileList.length > 0"
      label="处理后图片输出目录"
      @directory-selected="handleDirectorySelected"
      @error="handleError"
    />

    <!-- 预览区域 -->
    <div class="preview-section" v-if="fileList.length > 0 && fileList.length <= 6">
      <el-divider>图片预览</el-divider>
      <div class="image-preview-grid">
        <div 
          v-for="file in fileList" 
          :key="file.uid"
          class="preview-item"
        >
          <div class="preview-image">
            <img 
              :src="getPreviewUrl(file)" 
              :alt="file.name"
              @load="handleImageLoad"
              @error="handleImageError"
            />
            <div class="preview-overlay">
              <div class="preview-info">
                <div class="file-name">{{ file.name }}</div>
                <div class="file-size">{{ formatFileSize(file.size || 0) }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 任务控制 -->
    <TaskControls
      v-if="fileList.length > 0"
      mode="single"
      :status="taskStatus"
      :is-processing="isProcessing"
      :can-start="canStartProcessing"
      :can-pause="isProcessing"
      :can-stop="isProcessing"
      :show-batch-controls="false"
      :show-advanced="false"
      start-button-text="开始处理"
      processing-button-text="处理中..."
      @start="startProcessing"
      @pause="pauseProcessing"
      @stop="stopProcessing"
    />

    <!-- 处理进度 -->
    <div class="progress-section" v-if="processingTasks.length > 0">
      <el-divider>处理进度</el-divider>
      <TaskProgress
        :tasks="processingTasks"
        :allow-retry="true"
        :allow-pause="true"
        :allow-remove="true"
        :show-summary="processingTasks.length > 1"
        :show-result-info="true"
        @retry="retryTask"
        @pause="pauseTask"
        @remove="removeTask"
        @view-result="viewResult"
      />
    </div>

    <!-- 结果面板 -->
    <div class="results-section" v-if="processingResults.length > 0">
      <el-divider>处理结果</el-divider>
      <TaskResultPanel
        :results="processingResults"
        :allow-retry="true"
        empty-text="暂无处理结果"
        empty-hint="完成处理后结果将显示在这里"
        @retry="retryTaskFromResult"
        @remove="removeResult"
        @export-all="exportAllResults"
      />
    </div>

    <!-- 结果预览 -->
    <div class="result-preview-section" v-if="processingResults.length > 0 && processingResults.length <= 8">
      <el-divider>处理结果预览</el-divider>
      <ImageResultPreview
        :results="processingResults.filter(r => r.success)"
        @view-detail="viewResultDetail"
        @download="downloadResult"
      />
    </div>

    <!-- 批量操作 -->
    <div class="batch-section" v-if="fileList.length > 1">
      <el-divider>批量操作</el-divider>
      <div class="batch-actions">
        <el-button @click="createBatchTask">
          <Icon icon="mdi:playlist-plus" />
          创建批量任务
        </el-button>
        <el-button @click="previewProcessingEffect" :disabled="fileList.length === 0">
          <Icon icon="mdi:eye-outline" />
          预览处理效果
        </el-button>
        <el-button @click="exportAllResults" :disabled="processingResults.length === 0">
          <Icon icon="mdi:export" />
          导出所有结果
        </el-button>
      </div>
    </div>

    <!-- 处理效果预览弹窗 -->
    <el-dialog
      v-model="previewDialogVisible"
      title="处理效果预览"
      width="80%"
      :before-close="closePreviewDialog"
    >
      <ProcessingEffectPreview
        v-if="previewDialogVisible"
        :source-image="previewSourceImage"
        :options="processingOptions"
        @close="closePreviewDialog"
      />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Icon } from '@iconify/vue'
import type { UploadFile } from 'element-plus'

// 导入共享组件
import FileUploader from './shared/FileUploader.vue'
import OutputDirectorySelector from './shared/OutputDirectorySelector.vue'
import ProcessingOptions from './shared/ProcessingOptions.vue'
import TaskControls from './shared/TaskControls.vue'
import TaskProgress from './shared/TaskProgress.vue'
import TaskResultPanel from './shared/TaskResultPanel.vue'

// 导入Store
import { useMediaStore } from '@/stores/media-store'
import type { ProcessingOptions as ProcessingOptionsType } from '@/stores/media-store'

// 定义事件
const emit = defineEmits<{
  fileUploaded: [fileInfo: any]
  processingStarted: [taskInfo: any]
  processingCompleted: [result: any]
}>()

// 响应式数据
const mediaStore = useMediaStore()
const fileList = ref<UploadFile[]>([])
const outputDirectory = ref('')
const processingTasks = ref<any[]>([])
const processingResults = ref<any[]>([])
const isProcessing = ref(false)
const filePathMap = ref<Map<string, string>>(new Map())
const previewDialogVisible = ref(false)
const previewSourceImage = ref<string>('')

// 图片处理选项
const processingOptions = reactive<ProcessingOptionsType>({
  outputFormat: 'jpeg',
  quality: '80',
  resizeEnabled: false,
  width: 1920,
  height: 1080,
  maintainAspectRatio: true,
  optimize: true,
  progressive: false,
  stripMetadata: true,
  colorSpace: 'srgb',
  saturation: 1.0
})

// 计算属性
const taskStatus = computed(() => {
  if (isProcessing.value) return 'processing'
  if (processingTasks.value.some(t => t.status === 'completed')) return 'completed'
  if (processingTasks.value.some(t => t.status === 'error')) return 'error'
  return 'idle'
})

const canStartProcessing = computed(() => {
  return fileList.value.length > 0 && 
         outputDirectory.value && 
         !isProcessing.value
})

// 事件处理方法
const handleFileAdded = (file: UploadFile, filePath: string) => {
  filePathMap.value.set(file.uid.toString(), filePath)
  
  // 设置默认输出目录
  if (!outputDirectory.value) {
    outputDirectory.value = mediaStore.settings.defaultOutputDir
  }
  
  emit('fileUploaded', {
    name: file.name,
    size: file.size,
    path: filePath
  })
}

const handleFileRemoved = (file: UploadFile) => {
  filePathMap.value.delete(file.uid.toString())
  
  // 如果没有文件了，清空相关数据
  if (fileList.value.length === 0) {
    processingTasks.value = []
    processingResults.value = []
    isProcessing.value = false
  }
}

const handleDirectorySelected = (directory: string) => {
  console.log('输出目录已选择:', directory)
}

const handleError = (message: string) => {
  ElMessage.error(message)
}

const handlePresetSaved = (preset: any) => {
  ElMessage.success(`预设"${preset.label}"已保存`)
}

const handleImageLoad = () => {
  // 图片加载成功
}

const handleImageError = () => {
  console.warn('图片预览加载失败')
}

// 处理控制方法
const startProcessing = async () => {
  if (!canStartProcessing.value) {
    ElMessage.warning('请检查处理设置')
    return
  }

  isProcessing.value = true
  processingTasks.value = []

  try {
    // 为每个文件创建处理任务
    for (const file of fileList.value) {
      const filePath = filePathMap.value.get(file.uid.toString())
      if (!filePath) {
        ElMessage.warning(`未找到文件 ${file.name} 的路径`)
        continue
      }

      const outputFileName = generateOutputFileName(file.name)
      const outputPath = `${outputDirectory.value}/${outputFileName}`
      
      // 使用MediaStore创建单文件任务
      const taskId = await mediaStore.createSingleTask({
        type: 'image-process',
        fileName: file.name,
        filePath,
        outputPath,
        options: JSON.parse(JSON.stringify(processingOptions))
      })

      // 创建本地任务追踪
      const localTask = {
        id: taskId,
        fileName: file.name,
        inputPath: filePath,
        outputPath,
        status: 'pending',
        progress: 0,
        type: 'image-process'
      }
      
      processingTasks.value.push(localTask)
      
      // 启动任务
      startSingleTask(taskId, localTask)
    }

    emit('processingStarted', {
      taskCount: fileList.value.length,
      settings: processingOptions
    })

  } catch (error: any) {
    isProcessing.value = false
    ElMessage.error(`开始处理失败: ${error.message}`)
  }
}

const startSingleTask = async (taskId: string, localTask: any) => {
  try {
    localTask.status = 'processing'
    
    // 使用MediaStore启动任务
    await mediaStore.startSingleTask(taskId)
    
    // 获取任务结果
    const storeTask = mediaStore.singleTasks.get(taskId)
    if (storeTask) {
      localTask.status = storeTask.status
      localTask.progress = storeTask.progress
      
      if (storeTask.status === 'completed' && storeTask.result) {
        // 添加到结果列表
        processingResults.value.push(storeTask.result)
        
        ElMessage.success(`${localTask.fileName} 处理完成`)
        emit('processingCompleted', {
          taskId,
          result: storeTask.result
        })
      } else if (storeTask.status === 'error') {
        localTask.error = storeTask.error
        ElMessage.error(`${localTask.fileName} 处理失败: ${storeTask.error}`)
      }
    }
    
  } catch (error: any) {
    localTask.status = 'error'
    localTask.error = error.message
    ElMessage.error(`${localTask.fileName} 处理失败: ${error.message}`)
  }
  
  // 检查是否所有任务完成
  checkAllTasksCompleted()
}

const pauseProcessing = async () => {
  try {
    const processingTasksList = processingTasks.value.filter(t => t.status === 'processing')
    
    for (const task of processingTasksList) {
      await mediaStore.pauseSingleTask(task.id)
      task.status = 'paused'
    }
    
    isProcessing.value = false
    ElMessage.info('处理已暂停')
  } catch (error: any) {
    ElMessage.error(`暂停失败: ${error.message}`)
  }
}

const stopProcessing = async () => {
  try {
    await pauseProcessing()
    
    // 重置所有任务状态
    processingTasks.value.forEach(task => {
      if (['processing', 'paused'].includes(task.status)) {
        task.status = 'pending'
        task.progress = 0
      }
    })
    
    ElMessage.info('处理已停止')
  } catch (error: any) {
    ElMessage.error(`停止失败: ${error.message}`)
  }
}

// 任务操作方法
const retryTask = async (task: any) => {
  try {
    await mediaStore.retrySingleTask(task.id)
    
    const localTask = processingTasks.value.find(t => t.id === task.id)
    if (localTask) {
      localTask.status = 'pending'
      localTask.progress = 0
      localTask.error = undefined
    }
    
    ElMessage.success('任务重试已开始')
  } catch (error: any) {
    ElMessage.error(`重试失败: ${error.message}`)
  }
}

const pauseTask = async (task: any) => {
  try {
    await mediaStore.pauseSingleTask(task.id)
    
    const localTask = processingTasks.value.find(t => t.id === task.id)
    if (localTask) {
      localTask.status = 'paused'
    }
    
    ElMessage.info('任务已暂停')
  } catch (error: any) {
    ElMessage.error(`暂停失败: ${error.message}`)
  }
}

const removeTask = async (task: any) => {
  try {
    await mediaStore.removeSingleTask(task.id)
    
    const index = processingTasks.value.findIndex(t => t.id === task.id)
    if (index > -1) {
      processingTasks.value.splice(index, 1)
    }
    
    ElMessage.success('任务已移除')
  } catch (error: any) {
    ElMessage.error(`移除失败: ${error.message}`)
  }
}

const viewResult = async (task: any) => {
  const storeTask = mediaStore.singleTasks.get(task.id)
  if (storeTask?.result?.outputPath) {
    try {
      await window.electronAPI.app.showItemInFolder(storeTask.result.outputPath)
    } catch (error: any) {
      ElMessage.error(`打开文件失败: ${error.message}`)
    }
  }
}

// 结果操作方法
const retryTaskFromResult = async (result: any) => {
  const localTask = processingTasks.value.find(t => t.fileName === result.fileName)
  if (localTask) {
    await retryTask(localTask)
  }
}

const removeResult = (result: any) => {
  const index = processingResults.value.findIndex(r => r.id === result.id)
  if (index > -1) {
    processingResults.value.splice(index, 1)
  }
}

const exportAllResults = () => {
  // TODO: 实现批量导出功能
  ElMessage.info('批量导出功能开发中...')
}

// 预览相关方法
const getPreviewUrl = (file: UploadFile): string => {
  if (file.raw) {
    return URL.createObjectURL(file.raw)
  }
  return ''
}

const previewProcessingEffect = () => {
  if (fileList.value.length === 0) {
    ElMessage.warning('请先添加图片文件')
    return
  }
  
  // 使用第一个图片作为预览
  const firstFile = fileList.value[0]
  const filePath = filePathMap.value.get(firstFile.uid.toString())
  
  if (filePath) {
    previewSourceImage.value = filePath
    previewDialogVisible.value = true
  }
}

const closePreviewDialog = () => {
  previewDialogVisible.value = false
  previewSourceImage.value = ''
}

const viewResultDetail = (result: any) => {
  // TODO: 实现结果详情查看
  console.log('查看结果详情:', result)
}

const downloadResult = async (result: any) => {
  try {
    await window.electronAPI.app.showItemInFolder(result.outputPath)
  } catch (error: any) {
    ElMessage.error(`打开文件失败: ${error.message}`)
  }
}

// 批量操作方法
const createBatchTask = async () => {
  if (fileList.value.length === 0) {
    ElMessage.warning('请先添加文件')
    return
  }

  try {
    const files = fileList.value.map(file => ({
      fileName: file.name,
      filePath: filePathMap.value.get(file.uid.toString()) || file.name
    }))

    const batchId = await mediaStore.createBatchTask({
      name: `图片处理批量任务_${new Date().toLocaleString()}`,
      type: 'image-process',
      files,
      outputDirectory: outputDirectory.value,
      options: JSON.parse(JSON.stringify(processingOptions))
    })

    ElMessage.success('批量任务已创建')
    clearAll()
    
  } catch (error: any) {
    ElMessage.error(`创建批量任务失败: ${error.message}`)
  }
}

// 工具方法
const generateOutputFileName = (inputName: string): string => {
  const baseName = inputName.substring(0, inputName.lastIndexOf('.'))
  return `${baseName}.${processingOptions.outputFormat || 'jpeg'}`
}

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return (bytes / Math.pow(k, i)).toFixed(1) + ' ' + sizes[i]
}

const checkAllTasksCompleted = () => {
  const allCompleted = processingTasks.value.every(t => 
    ['completed', 'error'].includes(t.status)
  )
  
  if (allCompleted) {
    isProcessing.value = false
    
    const completedCount = processingTasks.value.filter(t => t.status === 'completed').length
    const failedCount = processingTasks.value.filter(t => t.status === 'error').length
    
    ElMessage.success(`处理完成: ${completedCount} 成功, ${failedCount} 失败`)
  }
}

const clearAll = () => {
  fileList.value = []
  processingTasks.value = []
  processingResults.value = []
  isProcessing.value = false
  filePathMap.value.clear()
}

// 生命周期
onMounted(async () => {
  if (!mediaStore.isInitialized) {
    await mediaStore.initialize()
  }
})
</script>

<style scoped lang="scss">
@use '@/assets/styles/index.scss' as *;

.image-processor {
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: $spacing-base;

    h3 {
      display: flex;
      align-items: center;
      gap: 8px;
      margin: 0;
      color: $text-primary;
      font-size: 16px;
      font-weight: 500;

      .section-icon {
        color: #13c2c2;
        font-size: 18px;
      }
    }
  }

  .options-section,
  .progress-section,
  .results-section,
  .result-preview-section {
    margin-top: $spacing-base;
  }

  .effect-preview-section {
    margin-top: $spacing-base;
  }

  .batch-section {
    margin-top: $spacing-base;
    
    .batch-actions {
      display: flex;
      gap: $spacing-base;
      justify-content: center;
    }
  }
}
</style>