import { BrowserWindow } from 'electron';
import { MediaService } from '../services/media-worker';

export interface ConvertOptions {
  outputFormat: string;
  quality?: string;
  maxWidth?: number;
  resizeEnabled?: boolean;
}

export interface ASROptions {
  language: string;
  outputFormats: string[];
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
}

/**
 * Media IPC 处理器类
 * 封装所有媒体相关的 IPC 处理逻辑，简化注册过程
 */
export class MediaIPCHandlers {
  constructor(private mediaService: MediaService) { }

  /**
   * 注册所有 IPC 处理器
   */
  register(ipcMain: Electron.IpcMain): void {
    // 核心媒体处理
    this.registerCoreHandlers(ipcMain);

    // 任务管理
    this.registerTaskHandlers(ipcMain);

    // 批量任务
    this.registerBatchHandlers(ipcMain);

    // 工具和设置
    this.registerUtilityHandlers(ipcMain);

    // 持久化
    this.registerPersistenceHandlers(ipcMain);
  }

  /**
   * 设置进度事件推送
   */
  setupProgressEvents(): void {
    this.mediaService.onTaskProgress = (taskId: string, progress: number) => {
      BrowserWindow.getAllWindows().forEach(window => {
        if (!window.isDestroyed()) {
          window.webContents.send('media-task-progress', { taskId, progress });
        }
      });
    };
  }

  private registerCoreHandlers(ipcMain: Electron.IpcMain): void {
    // 视频转换
    ipcMain.handle('media-convert-video', async (_, inputPath: string, outputPath: string, options: ConvertOptions): Promise<ApiResponse<string>> => {
      return this.executeWithErrorHandling(
        () => this.mediaService.convertVideo(inputPath, outputPath, options),
        `转换视频: ${inputPath} -> ${outputPath}`
      );
    });

    // 音频提取
    ipcMain.handle('media-extract-audio', async (_, videoPath: string, audioPath: string, options?: { quality?: string }): Promise<ApiResponse<string>> => {
      return this.executeWithErrorHandling(
        () => this.mediaService.extractAudio(videoPath, audioPath, options),
        `提取音频: ${videoPath} -> ${audioPath}`
      );
    });

    // 语音识别
    ipcMain.handle('media-extract-text', async (_, audioPath: string, options: ASROptions): Promise<ApiResponse<{ text: string; srt: string }>> => {
      return this.executeWithErrorHandling(
        () => this.mediaService.extractTextFromAudio(audioPath, options),
        `语音识别: ${audioPath}`
      );
    });

    // 图片处理
    ipcMain.handle('media-process-images', async (_, imagePaths: string[], options: ConvertOptions): Promise<ApiResponse<string[]>> => {
      return this.executeWithErrorHandling(
        () => this.mediaService.processImages(imagePaths, options),
        `批量处理图片: ${imagePaths.length} 个文件`
      );
    });
  }

  private registerTaskHandlers(ipcMain: Electron.IpcMain): void {
    // 任务控制
    ipcMain.handle('media-pause-task', this.createBooleanHandler(
      async (_, taskId: string) => {
        await this.mediaService.pauseTask(taskId);
      },
      '暂停任务'
    ));

    ipcMain.handle('media-resume-task', this.createBooleanHandler(
      async (_, taskId: string) => {
        await this.mediaService.resumeTask(taskId);
      },
      '恢复任务'
    ));

    ipcMain.handle('media-cancel-task', this.createBooleanHandler(
      async (_, taskId: string) => {
        await this.mediaService.cancelTask(taskId);
      },
      '取消任务'
    ));

    // 任务状态查询
    ipcMain.handle('media-get-task-status', async (_, taskId: string): Promise<ApiResponse<any>> => {
      return this.executeWithErrorHandling(
        () => this.mediaService.getTaskStatus(taskId),
        `获取任务状态: ${taskId}`
      );
    });

    // 获取活动任务
    ipcMain.handle('media-get-active-tasks', async (): Promise<ApiResponse<string[]>> => {
      return this.executeWithErrorHandling(
        () => this.mediaService['ffmpegService']?.getActiveTasks() || [],
        '获取活动任务'
      );
    });
  }

  private registerBatchHandlers(ipcMain: Electron.IpcMain): void {
    // 批量任务管理  
    ipcMain.handle('media-create-batch-task', async (_, tasks: any[]): Promise<ApiResponse<string>> => {
      return this.executeWithErrorHandling(
        () => this.mediaService.createBatchTask(tasks),
        `创建批量任务: ${tasks.length} 个任务`
      );
    });

    ipcMain.handle('media-start-batch-task', this.createBooleanHandler(
      async (_, batchId: string) => {
        await this.mediaService.startBatchTask(batchId);
      },
      '开始批量任务'
    ));

    ipcMain.handle('media-pause-batch-task', this.createBooleanHandler(
      async (_, batchId: string) => {
        await this.mediaService.pauseBatchTask(batchId);
      },
      '暂停批量任务'
    ));

    ipcMain.handle('media-cancel-batch-task', this.createBooleanHandler(
      async (_, batchId: string) => {
        await this.mediaService.cancelBatchTask(batchId);
      },
      '取消批量任务'
    ));

    ipcMain.handle('media-get-batch-progress', async (_, batchId: string): Promise<ApiResponse<any>> => {
      return this.executeWithErrorHandling(
        () => this.mediaService.getBatchProgress(batchId),
        `获取批量任务进度: ${batchId}`
      );
    });

    ipcMain.handle('media-retry-task', this.createBooleanHandler(
      async (_, batchId: string, taskId: string) => {
        await this.mediaService.retryTask(batchId, taskId);
      },
      '重试任务'
    ));
  }

  private registerUtilityHandlers(ipcMain: Electron.IpcMain): void {
    // 格式支持
    ipcMain.handle('media-get-supported-formats', async (): Promise<ApiResponse<{ video: string[]; audio: string[]; image: string[] }>> => {
      return this.executeWithErrorHandling(
        () => this.mediaService.getSupportedFormats(),
        '获取支持格式'
      );
    });

    // 文件信息
    ipcMain.handle('media-get-file-info', async (_, filePath: string): Promise<ApiResponse<any>> => {
      return this.executeWithErrorHandling(
        () => this.mediaService.getFileInfo(filePath),
        `获取文件信息: ${filePath}`
      );
    });

    // FFmpeg 状态
    ipcMain.handle('media-check-ffmpeg-status', async (): Promise<ApiResponse<any>> => {
      return this.executeWithErrorHandling(
        () => this.mediaService.checkFFmpegStatus(),
        '检查FFmpeg状态'
      );
    });

    // 清理
    ipcMain.handle('media-cleanup-temp', this.createBooleanHandler(
      async () => {
        await this.mediaService.cleanupTempFiles();
      },
      '清理临时文件'
    ));

    // 统计
    ipcMain.handle('media-get-stats', async (): Promise<ApiResponse<any>> => {
      return this.executeWithErrorHandling(
        async () => {
          // 从新的统计存储中获取数据
          const Store = (await import('electron-store')).default;
          const store = new Store();
          return store.get('processingStats', {
            totalProcessed: 0,
            totalSize: 0,
            totalTime: 0,
            successRate: 0,
            averageProcessingTime: 0,
            tasksByType: {},
            tasksByStatus: {},
            lastUpdated: Date.now()
          });
        },
        '获取处理统计'
      );
    });

    ipcMain.handle('media-update-stats', async (): Promise<ApiResponse<boolean>> => {
      console.log('[IPC] 统计信息已更新');
      return { success: true, data: true };
    });

    // 添加统计数据刷新通知处理器
    ipcMain.handle('media-notify-stats-update', async (): Promise<ApiResponse<boolean>> => {
      console.log('[IPC] 收到统计数据更新通知');
      // 这里可以添加额外的处理逻辑，比如通知其他窗口
      return { success: true, data: true };
    });
  }

  /**
   * 创建一个返回 boolean 的 IPC 处理程序
   */
  private createBooleanHandler(
    handler: (event: Electron.IpcMainInvokeEvent, ...args: any[]) => Promise<void>,
    description: string
  ): (event: Electron.IpcMainInvokeEvent, ...args: any[]) => Promise<ApiResponse<boolean>> {
    return async (event: Electron.IpcMainInvokeEvent, ...args: any[]): Promise<ApiResponse<boolean>> => {
      console.log(`[IPC] ${description}`);
      try {
        await handler(event, ...args);
        return { success: true, data: true };
      } catch (error: unknown) {
        console.error(`[IPC] ${description} 失败:`, error);
        return {
          success: false,
          error: error instanceof Error ? error.message : '未知错误',
          data: false
        };
      }
    };
  }

  private registerPersistenceHandlers(ipcMain: Electron.IpcMain): void {
    // 设置管理
    ipcMain.handle('media-get-settings', async (): Promise<ApiResponse<any>> => {
      return this.executeWithErrorHandling(async () => {
        const Store = (await import('electron-store')).default;
        const store = new Store();

        const defaultSettings = {
          maxConcurrentTasks: 3,
          autoCleanupCompleted: true,
          cleanupDelay: 24 * 60 * 60 * 1000,
          defaultOutputDirectory: '',
          enableProgressNotifications: true
        };

        return store.get('mediaSettings', defaultSettings);
      }, '加载媒体设置');
    });

    ipcMain.handle('media-save-settings', this.createBooleanHandler(
      async (_, settings: any) => {
        const Store = (await import('electron-store')).default;
        const store = new Store();

        store.set('mediaSettings', settings);
      },
      '保存媒体设置'
    ));

    // 任务持久化
    ipcMain.handle('media-persist-task', this.createBooleanHandler(
      async (_, task: any) => {
        const Store = (await import('electron-store')).default;
        const store = new Store();

        const savedTasks = store.get('singleTasks', {}) as Record<string, any>;
        savedTasks[task.id] = task;
        store.set('singleTasks', savedTasks);
      },
      '持久化单文件任务'
    ));

    ipcMain.handle('media-persist-batch', this.createBooleanHandler(
      async (_, batch: any) => {
        const Store = (await import('electron-store')).default;
        const store = new Store();

        const savedBatches = store.get('batchTasks', {}) as Record<string, any>;
        savedBatches[batch.id] = batch;
        store.set('batchTasks', savedBatches);
      },
      '持久化批量任务'
    ));

    ipcMain.handle('media-delete-task', this.createBooleanHandler(
      async (_, taskId: string) => {
        const Store = (await import('electron-store')).default;
        const store = new Store();

        const savedTasks = store.get('singleTasks', {}) as Record<string, any>;

        if (savedTasks[taskId]) {
          delete savedTasks[taskId];
          store.set('singleTasks', savedTasks);
        }
      },
      '删除单文件任务'
    ));

    ipcMain.handle('media-delete-batch', this.createBooleanHandler(
      async (_, batchId: string) => {
        const Store = (await import('electron-store')).default;
        const store = new Store();

        const savedBatches = store.get('batchTasks', {}) as Record<string, any>;

        if (savedBatches[batchId]) {
          delete savedBatches[batchId];
          store.set('batchTasks', savedBatches);
        }
      },
      '删除批量任务'
    ));

    ipcMain.handle('media-clear-all-tasks', this.createBooleanHandler(
      async () => {
        const Store = (await import('electron-store')).default;
        const store = new Store();

        store.set('singleTasks', {});
        store.set('batchTasks', {});
      },
      '清空所有任务'
    ));

    // 结果持久化
    this.registerResultPersistence(ipcMain);

    // 统计持久化
    this.registerStatsPersistence(ipcMain);

    // 未完成任务查询
    ipcMain.handle('media-get-uncompleted-tasks', async (): Promise<ApiResponse<{ singleTasks: any[], batchTasks: any[] }>> => {
      return this.executeWithErrorHandling(async () => {
        const Store = (await import('electron-store')).default;
        const store = new Store();

        const singleTasksObj = store.get('singleTasks', {}) as Record<string, any>;
        const batchTasksObj = store.get('batchTasks', {}) as Record<string, any>;

        const singleTasks = Object.values(singleTasksObj).filter(
          task => ['pending', 'processing', 'paused'].includes(task.status)
        );

        const batchTasks = Object.values(batchTasksObj).filter(
          batch => ['pending', 'processing', 'paused'].includes(batch.status)
        );

        return { singleTasks, batchTasks };
      }, '获取未完成任务');
    });
  }

  private registerResultPersistence(ipcMain: Electron.IpcMain): void {
    ipcMain.handle('media-persist-task-result', this.createBooleanHandler(
      async (_, result: any) => {
        const Store = (await import('electron-store')).default;
        const store = new Store();

        const existingResults = store.get('taskResults', []) as any[];
        const existingIndex = existingResults.findIndex(r => r.id === result.id);

        if (existingIndex >= 0) {
          existingResults[existingIndex] = result;
        } else {
          existingResults.push(result);
        }

        if (existingResults.length > 1000) {
          existingResults.splice(0, existingResults.length - 1000);
        }

        store.set('taskResults', existingResults);
      },
      '持久化任务结果'
    ));

    ipcMain.handle('media-get-task-results', async (): Promise<ApiResponse<any[]>> => {
      return this.executeWithErrorHandling(async () => {
        const Store = (await import('electron-store')).default;
        const store = new Store();
        return store.get('taskResults', []) as any[];
      }, '获取任务结果');
    });

    ipcMain.handle('media-delete-task-result', this.createBooleanHandler(
      async (_, resultId: string) => {
        const Store = (await import('electron-store')).default;
        const store = new Store();
        const results = store.get('taskResults', []) as any[];
        const filteredResults = results.filter(r => r.id !== resultId);
        store.set('taskResults', filteredResults);
      },
      '删除任务结果'
    ));

    ipcMain.handle('media-clear-task-results', this.createBooleanHandler(
      async () => {
        const Store = (await import('electron-store')).default;
        const store = new Store();
        store.set('taskResults', []);
      },
      '清空所有任务结果'
    ));
  }

  private registerStatsPersistence(ipcMain: Electron.IpcMain): void {
    ipcMain.handle('media-persist-stats', async (_, stats: any): Promise<ApiResponse<boolean>> => {
      console.log(`[IPC] 持久化统计数据: ${JSON.stringify(stats)}`);
      try {
        const Store = (await import('electron-store')).default;
        const store = new Store();

        // 添加时间戳
        const statsWithTimestamp = {
          ...stats,
          lastUpdated: Date.now()
        };

        // 保存到存储
        store.set('processingStats', statsWithTimestamp);

        console.log(`[IPC] 统计数据持久化成功: 已处理 ${stats.totalProcessed || 0} 个任务`);
        return { success: true, data: true };
      } catch (error: unknown) {
        console.error(`[IPC] 持久化统计数据失败:`, error);
        return {
          success: false,
          error: error instanceof Error ? error.message : '未知错误',
          data: false
        };
      }
    });

    ipcMain.handle('media-clear-stats', this.createBooleanHandler(
      async () => {
        try {
          // 清空存储中的统计数据
          const Store = (await import('electron-store')).default;
          const store = new Store();
          store.delete('processingStats');
          // singleTasks
          store.delete('singleTasks');
          // batchTasks
          store.delete('batchTasks');
          // taskResults
          store.delete('taskResults');
          console.log('[IPC] 统计数据已清空');
        } catch (error) {
          console.error('[IPC] 清空统计数据失败:', error);
          throw error;
        }
      },
      '清空统计数据'
    ));
  }

  /**
   * 统一的错误处理包装器
   */
  private async executeWithErrorHandling<T>(
    operation: () => Promise<T> | T,
    description: string
  ): Promise<ApiResponse<T>> {
    console.log(`[IPC] ${description}`);
    try {
      const result = await operation();
      return { success: true, data: result };
    } catch (error: unknown) {
      console.error(`[IPC] ${description} 失败:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      };
    }
  }
}